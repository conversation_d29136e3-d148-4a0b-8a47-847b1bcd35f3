import { Href, <PERSON> } from 'expo-router';
import React, { useState } from 'react';
import {
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { authStyles } from '../styles';

interface AuthFormProps {
  title: string;
  subtitle: string;
  email: string;
  password: string;
  confirmPassword?: string;
  loading: boolean;
  buttonText: string;
  loadingText: string;
  linkText: string;
  linkHref: Href;
  linkButtonText: string;
  showConfirmPassword?: boolean;
  onEmailChange: (email: string) => void;
  onPasswordChange: (password: string) => void;
  onConfirmPasswordChange?: (password: string) => void;
  onSubmit: () => void;
}

export default function AuthForm({
  title,
  subtitle,
  email,
  password,
  confirmPassword,
  loading,
  buttonText,
  loadingText,
  linkText,
  linkHref,
  linkButtonText,
  showConfirmPassword = false,
  onEmailChange,
  onPasswordChange,
  onConfirmPasswordChange,
  onSubmit,
}: AuthFormProps) {
  const [emailFocused, setEmailFocused] = useState(false);
  const [passwordFocused, setPasswordFocused] = useState(false);
  const [confirmPasswordFocused, setConfirmPasswordFocused] = useState(false);

  return (
    <KeyboardAvoidingView
      style={authStyles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Decorative background pattern */}
      <View style={authStyles.decorativePattern} />

      <ScrollView contentContainerStyle={authStyles.scrollContainer}>
        {/* Header with Islamic branding */}
        <View style={authStyles.headerContainer}>
          <View style={authStyles.islamicDecoration}>
            <Text style={authStyles.islamicIcon}>☪</Text>
          </View>
        </View>

        {/* Main form container */}
        <View style={authStyles.formContainer}>
          <Text style={authStyles.title}>{title}</Text>
          <Text style={authStyles.subtitle}>{subtitle}</Text>

          <View style={authStyles.inputContainer}>
            <Text style={authStyles.label}>Email</Text>
            <TextInput
              style={[
                authStyles.input,
                emailFocused && authStyles.inputFocused
              ]}
              value={email}
              onChangeText={onEmailChange}
              onFocus={() => setEmailFocused(true)}
              onBlur={() => setEmailFocused(false)}
              placeholder="Masukkan email Anda"
              placeholderTextColor="#9CA3AF"
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <View style={authStyles.inputContainer}>
            <Text style={authStyles.label}>Kata Sandi</Text>
            <TextInput
              style={[
                authStyles.input,
                passwordFocused && authStyles.inputFocused
              ]}
              value={password}
              onChangeText={onPasswordChange}
              onFocus={() => setPasswordFocused(true)}
              onBlur={() => setPasswordFocused(false)}
              placeholder="Masukkan kata sandi Anda"
              placeholderTextColor="#9CA3AF"
              secureTextEntry
              autoCapitalize="none"
            />
          </View>

          {showConfirmPassword && (
            <View style={authStyles.inputContainer}>
              <Text style={authStyles.label}>Konfirmasi Kata Sandi</Text>
              <TextInput
                style={[
                  authStyles.input,
                  confirmPasswordFocused && authStyles.inputFocused
                ]}
                value={confirmPassword}
                onChangeText={onConfirmPasswordChange}
                onFocus={() => setConfirmPasswordFocused(true)}
                onBlur={() => setConfirmPasswordFocused(false)}
                placeholder="Konfirmasi kata sandi Anda"
                placeholderTextColor="#9CA3AF"
                secureTextEntry
                autoCapitalize="none"
              />
            </View>
          )}

          <TouchableOpacity
            style={[authStyles.button, loading && authStyles.buttonDisabled]}
            onPress={onSubmit}
            disabled={loading}
            activeOpacity={0.8}
          >
            {loading ? (
              <View style={authStyles.loadingContainer}>
                <ActivityIndicator size="small" color="#FFFFFF" />
                <Text style={authStyles.loadingText}>{loadingText}</Text>
              </View>
            ) : (
              <Text style={authStyles.buttonText}>{buttonText}</Text>
            )}
          </TouchableOpacity>

          <View style={authStyles.linkContainer}>
            <Text style={authStyles.linkText}>{linkText} </Text>
            <Link href={linkHref} style={authStyles.link}>
              <Text style={authStyles.linkTextBold}>{linkButtonText}</Text>
            </Link>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
