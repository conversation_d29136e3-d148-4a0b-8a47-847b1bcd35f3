import { StyleSheet } from 'react-native';

export const authStyles = StyleSheet.create({
  // Main container with clean background
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC', // Light blue-gray background
  },

  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
    minHeight: '100%',
  },

  // Header section with Islamic branding - FIXED SIZE
  headerContainer: {
    alignItems: 'center',
    marginBottom: 32,
    paddingTop: 40,
  },

  // Islamic decorative element - SMALLER AND MORE SUBTLE
  islamicDecoration: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#0055AA',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    shadowColor: '#0055AA',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },

  islamicIcon: {
    fontSize: 24,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },

  // Form container with card-like appearance - IMPROVED LAYOUT
  formContainer: {
    width: '100%',
    maxWidth: 360, // Slightly smaller for better mobile experience
    alignSelf: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 16, // Slightly smaller radius
    padding: 24, // Reduced padding for better balance
    marginHorizontal: 16, // Ensure proper margins
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },

  // Typography - IMPROVED HIERARCHY
  title: {
    fontSize: 28, // Slightly smaller but still elderly-friendly
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#0055AA', // Primary blue color
    lineHeight: 36,
  },

  subtitle: {
    fontSize: 16, // Balanced size for readability
    textAlign: 'center',
    marginBottom: 32, // Proper spacing
    color: '#4A5568',
    lineHeight: 22,
  },

  // Input styling - IMPROVED BALANCE
  inputContainer: {
    marginBottom: 20, // Balanced spacing
  },

  label: {
    fontSize: 16, // Balanced label size
    fontWeight: '600',
    marginBottom: 8,
    color: '#1A202C',
  },

  input: {
    borderWidth: 1, // Standard border thickness
    borderColor: '#E2E8F0',
    borderRadius: 12, // Moderate rounded corners
    padding: 16, // Comfortable padding
    fontSize: 16, // Standard readable size
    backgroundColor: '#F8FAFC',
    minHeight: 48, // Standard touch target
    color: '#1A202C',
  },

  inputFocused: {
    borderColor: '#0055AA',
    backgroundColor: '#FFFFFF',
    shadowColor: '#0055AA',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },

  // Button styling - IMPROVED BALANCE
  button: {
    backgroundColor: '#0055AA',
    borderRadius: 12,
    padding: 16, // Comfortable padding
    alignItems: 'center',
    marginTop: 20,
    minHeight: 48, // Standard touch target
    shadowColor: '#0055AA',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 3,
  },

  buttonDisabled: {
    backgroundColor: '#CBD5E0',
    shadowOpacity: 0,
    elevation: 0,
  },

  buttonText: {
    color: '#FFFFFF',
    fontSize: 16, // Balanced button text
    fontWeight: '700',
    letterSpacing: 0.3,
  },

  // Link styling - IMPROVED SPACING
  linkContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 24,
    paddingVertical: 8,
  },

  linkText: {
    fontSize: 14,
    color: '#4A5568',
    lineHeight: 20,
  },

  link: {
    marginLeft: 8,
    paddingVertical: 8,
    paddingHorizontal: 4,
  },

  linkTextBold: {
    fontSize: 14,
    color: '#0055AA',
    fontWeight: '700',
    textDecorationLine: 'underline',
  },

  // Loading indicator styling
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  loadingText: {
    marginLeft: 12,
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: '600',
  },
});
