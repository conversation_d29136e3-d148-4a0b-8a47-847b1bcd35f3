import { StyleSheet } from 'react-native';

export const authStyles = StyleSheet.create({
  // Main container with Islamic-inspired gradient background
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC', // Light blue-gray background
  },

  // Gradient background overlay
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    backgroundColor: 'transparent',
  },

  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 24, // Increased padding for elderly-friendly spacing
    minHeight: '100%',
  },

  // Header section with Islamic branding
  headerContainer: {
    alignItems: 'center',
    marginBottom: 40,
    paddingTop: 20,
  },

  // Islamic decorative element
  islamicDecoration: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#0055AA',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
    shadowColor: '#0055AA',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },

  islamicIcon: {
    fontSize: 32,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },

  // Form container with card-like appearance
  formContainer: {
    width: '100%',
    maxWidth: 400,
    alignSelf: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 20, // Rounded corners for modern look
    padding: 32, // Generous padding for elderly-friendly design
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 8,
  },

  // Typography - elderly-friendly sizes
  title: {
    fontSize: 32, // Larger font for elderly users
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 12,
    color: '#0055AA', // Primary blue color
    lineHeight: 40,
  },

  subtitle: {
    fontSize: 18, // Increased from 16 for better readability
    textAlign: 'center',
    marginBottom: 40, // More spacing
    color: '#4A5568',
    lineHeight: 24,
  },

  // Input styling with elderly-friendly design
  inputContainer: {
    marginBottom: 24, // Increased spacing between inputs
  },

  label: {
    fontSize: 18, // Larger label text
    fontWeight: '600',
    marginBottom: 12,
    color: '#1A202C',
  },

  input: {
    borderWidth: 2, // Thicker border for better visibility
    borderColor: '#E2E8F0',
    borderRadius: 16, // Rounded corners
    padding: 20, // Larger touch target (48px minimum height)
    fontSize: 18, // Larger text for readability
    backgroundColor: '#F8FAFC',
    minHeight: 56, // Elderly-friendly touch target
    color: '#1A202C',
  },

  inputFocused: {
    borderColor: '#0055AA',
    backgroundColor: '#FFFFFF',
    shadowColor: '#0055AA',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },

  // Button styling with Islamic blue theme
  button: {
    backgroundColor: '#0055AA',
    borderRadius: 16,
    padding: 20, // Large touch target
    alignItems: 'center',
    marginTop: 24,
    minHeight: 56, // Elderly-friendly touch target
    shadowColor: '#0055AA',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },

  buttonDisabled: {
    backgroundColor: '#CBD5E0',
    shadowOpacity: 0,
    elevation: 0,
  },

  buttonText: {
    color: '#FFFFFF',
    fontSize: 18, // Larger button text
    fontWeight: '700',
    letterSpacing: 0.5,
  },

  // Link styling
  linkContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 32,
    paddingVertical: 8,
  },

  linkText: {
    fontSize: 16,
    color: '#4A5568',
    lineHeight: 24,
  },

  link: {
    marginLeft: 8,
    paddingVertical: 8,
    paddingHorizontal: 4,
  },

  linkTextBold: {
    fontSize: 16,
    color: '#0055AA',
    fontWeight: '700',
    textDecorationLine: 'underline',
  },

  // Islamic-themed decorative elements
  decorativePattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 120,
    opacity: 0.05,
    backgroundColor: '#0055AA',
  },

  // Loading indicator styling
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  loadingText: {
    marginLeft: 12,
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: '600',
  },
});
